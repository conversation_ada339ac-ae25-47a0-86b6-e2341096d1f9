'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { db, HielProfile, HielSettings, HielLink, supabase } from '@/lib/supabase';
import { motion } from 'framer-motion';
import HielLinkEditor from './HielLinkEditor';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface HielProfileEditorProps {
  profile: HielProfile | null;
  settings: HielSettings | null;
  onSave: () => void;
  onCancel: () => void;
}

export default function HielProfileEditor({
  profile,
  settings,
  onSave,
  onCancel,
}: HielProfileEditorProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [checkingUsername, setCheckingUsername] = useState(false);
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingBackground, setUploadingBackground] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    username: '',
    business_name: '',
    description: '',
    logo_url: '',
    background_image_url: '',
    theme_color: '#3B82F6',
    text_color: '#FFFFFF',
    location: '',
    phone: '',
    email: '',
    website: '',
    status: 'draft' as 'draft' | 'published' | 'suspended',
  });

  const [links, setLinks] = useState<Partial<HielLink>[]>([]);

  useEffect(() => {
    if (profile) {
      setFormData({
        username: profile.username,
        business_name: profile.business_name,
        description: profile.description || '',
        logo_url: profile.logo_url || '',
        background_image_url: profile.background_image_url || '',
        theme_color: profile.theme_color,
        text_color: profile.text_color,
        location: profile.location || '',
        phone: profile.phone || '',
        email: profile.email || '',
        website: profile.website || '',
        status: profile.status,
      });
      setLinks(profile.links || []);
      setUsernameAvailable(true); // Existing username is available
    } else {
      // Reset form for new profile
      setFormData({
        username: '',
        business_name: '',
        description: '',
        logo_url: '',
        background_image_url: '',
        theme_color: '#3B82F6',
        text_color: '#FFFFFF',
        location: '',
        phone: '',
        email: '',
        website: '',
        status: 'draft',
      });
      setLinks([]);
      setUsernameAvailable(null);
    }
  }, [profile]);

  const checkUsername = async (username: string) => {
    if (!username || username === profile?.username) {
      setUsernameAvailable(profile ? true : null);
      return;
    }

    setCheckingUsername(true);
    try {
      const available = await db.checkUsernameAvailability(username);
      setUsernameAvailable(available);
    } catch (error) {
      console.error('Error checking username:', error);
      setUsernameAvailable(null);
    } finally {
      setCheckingUsername(false);
    }
  };

  const handleUsernameChange = (username: string) => {
    // Clean username: lowercase, alphanumeric, dots, and underscores only
    const cleanUsername = username.toLowerCase().replace(/[^a-z0-9._]/g, '');
    setFormData(prev => ({ ...prev, username: cleanUsername }));

    // Debounce username check
    const timeoutId = setTimeout(() => checkUsername(cleanUsername), 500);
    return () => clearTimeout(timeoutId);
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    setUploadingLogo(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}-logo-${Date.now()}.${fileExt}`;

      const { data, error } = await supabase.storage
        .from('hiellinks')
        .upload(fileName, file);

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('hiellinks')
        .getPublicUrl(fileName);

      setFormData(prev => ({ ...prev, logo_url: publicUrl }));
    } catch (error) {
      console.error('Error uploading logo:', error);
      alert('Failed to upload logo. Please try again.');
    } finally {
      setUploadingLogo(false);
    }
  };

  const handleBackgroundUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    setUploadingBackground(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}-bg-${Date.now()}.${fileExt}`;

      const { data, error } = await supabase.storage
        .from('hiellinks')
        .upload(fileName, file);

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('hiellinks')
        .getPublicUrl(fileName);

      setFormData(prev => ({ ...prev, background_image_url: publicUrl }));
    } catch (error) {
      console.error('Error uploading background:', error);
      alert('Failed to upload background image. Please try again.');
    } finally {
      setUploadingBackground(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !formData.username || !formData.business_name) {
      alert('Please fill in all required fields.');
      return;
    }

    if (usernameAvailable === false) {
      alert('Username is not available. Please choose a different one.');
      return;
    }

    setLoading(true);
    try {
      console.log('Form data being submitted:', formData);
      console.log('User ID:', user.id);

      let savedProfile: HielProfile | null;

      if (profile) {
        // Update existing profile
        console.log('Updating existing profile:', profile.id);
        savedProfile = await db.updateHielProfile(profile.id, {
          ...formData,
          user_id: user.id,
        });
      } else {
        // Create new profile
        console.log('Creating new profile');
        const profileData = {
          ...formData,
          user_id: user.id,
        };
        console.log('Profile data to create:', profileData);
        savedProfile = await db.createHielProfile(profileData);
      }

      if (!savedProfile) {
        throw new Error('Failed to save profile');
      }

      // Save links
      if (profile) {
        // Delete existing links and create new ones
        const existingLinks = profile.links || [];
        for (const link of existingLinks) {
          await db.deleteHielLink(link.id);
        }
      }

      // Create new links
      for (let i = 0; i < links.length; i++) {
        const link = links[i];
        if (link.title && link.url) {
          await db.createHielLink({
            ...link,
            profile_id: savedProfile.id,
            sort_order: i,
          });
        }
      }

      onSave();
    } catch (error) {
      console.error('Error saving profile:', error);

      // Show more specific error messages
      let errorMessage = 'Failed to save profile. Please try again.';
      if (error instanceof Error) {
        if (error.message.includes('username')) {
          errorMessage = 'Username is already taken or invalid. Please choose a different one.';
        } else if (error.message.includes('business_name')) {
          errorMessage = 'Business name is required.';
        } else if (error.message.includes('user_id')) {
          errorMessage = 'Authentication error. Please log out and log back in.';
        }
      }

      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const addLink = () => {
    if (!settings || links.length < settings.max_links_per_profile) {
      setLinks(prev => [...prev, {
        title: '',
        url: '',
        type: 'custom',
        is_active: true,
      }]);
    }
  };

  const updateLink = (index: number, linkData: Partial<HielLink>) => {
    setLinks(prev => prev.map((link, i) => i === index ? { ...link, ...linkData } : link));
  };

  const removeLink = (index: number) => {
    setLinks(prev => prev.filter((_, i) => i !== index));
  };

  const canAddLink = !settings || links.length < settings.max_links_per_profile;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {profile ? 'Edit Profile' : 'Create New Profile'}
        </h2>
        <button
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          Cancel
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Basic Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Username *
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleUsernameChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="your-username"
                  required
                />
                {checkingUsername && (
                  <div className="absolute right-3 top-2.5">
                    <LoadingSpinner size="sm" />
                  </div>
                )}
              </div>
              {formData.username && (
                <div className="mt-1 text-sm">
                  {checkingUsername ? (
                    <span className="text-gray-500">Checking availability...</span>
                  ) : usernameAvailable === true ? (
                    <span className="text-green-600">✓ Username available</span>
                  ) : usernameAvailable === false ? (
                    <span className="text-red-600">✗ Username not available</span>
                  ) : null}
                </div>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Your profile will be available at: /hielLinks/{formData.username}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Business Name *
              </label>
              <input
                type="text"
                value={formData.business_name}
                onChange={(e) => setFormData(prev => ({ ...prev, business_name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Your Business Name"
                required
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Brief description of your business..."
            />
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Contact Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Email
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Phone
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="+****************"
              />
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Website
              </label>
              <input
                type="url"
                value={formData.website}
                onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="https://your-website.com"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Location
              </label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="City, Country"
              />
            </div>
          </div>
        </div>

        {/* Media Upload */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Media & Branding
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Logo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Logo
              </label>
              <div className="space-y-3">
                {formData.logo_url && (
                  <div className="relative">
                    <img
                      src={formData.logo_url}
                      alt="Logo preview"
                      className="w-20 h-20 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
                    />
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, logo_url: '' }))}
                      className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors"
                    >
                      ×
                    </button>
                  </div>
                )}
                <div className="flex space-x-2">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    disabled={uploadingLogo}
                    className="hidden"
                    id="logo-upload"
                  />
                  <label
                    htmlFor="logo-upload"
                    className={`px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium cursor-pointer transition-colors ${
                      uploadingLogo
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    {uploadingLogo ? 'Uploading...' : 'Upload Logo'}
                  </label>
                  <input
                    type="url"
                    value={formData.logo_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, logo_url: e.target.value }))}
                    placeholder="Or paste image URL"
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Background Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Background Image
              </label>
              <div className="space-y-3">
                {formData.background_image_url && (
                  <div className="relative">
                    <img
                      src={formData.background_image_url}
                      alt="Background preview"
                      className="w-full h-20 rounded-lg object-cover border border-gray-200 dark:border-gray-600"
                    />
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, background_image_url: '' }))}
                      className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors"
                    >
                      ×
                    </button>
                  </div>
                )}
                <div className="flex space-x-2">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleBackgroundUpload}
                    disabled={uploadingBackground}
                    className="hidden"
                    id="background-upload"
                  />
                  <label
                    htmlFor="background-upload"
                    className={`px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium cursor-pointer transition-colors ${
                      uploadingBackground
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    {uploadingBackground ? 'Uploading...' : 'Upload Background'}
                  </label>
                  <input
                    type="url"
                    value={formData.background_image_url}
                    onChange={(e) => setFormData(prev => ({ ...prev, background_image_url: e.target.value }))}
                    placeholder="Or paste image URL"
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Theme Customization */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Theme Customization
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Theme Color
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  value={formData.theme_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, theme_color: e.target.value }))}
                  className="w-12 h-10 border border-gray-300 dark:border-gray-600 rounded-md"
                />
                <input
                  type="text"
                  value={formData.theme_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, theme_color: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Text Color
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  value={formData.text_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, text_color: e.target.value }))}
                  className="w-12 h-10 border border-gray-300 dark:border-gray-600 rounded-md"
                />
                <input
                  type="text"
                  value={formData.text_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, text_color: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Links Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Links ({links.length}{settings ? `/${settings.max_links_per_profile}` : ''})
            </h3>
            {canAddLink && (
              <button
                type="button"
                onClick={addLink}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
              >
                Add Link
              </button>
            )}
          </div>

          <div className="space-y-4">
            {links.map((link, index) => (
              <HielLinkEditor
                key={index}
                link={link}
                onChange={(linkData) => updateLink(index, linkData)}
                onRemove={() => removeLink(index)}
              />
            ))}
            
            {links.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No links added yet. Click "Add Link" to get started.
              </div>
            )}
          </div>

          {!canAddLink && (
            <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                You've reached your maximum number of links ({settings?.max_links_per_profile}).
              </p>
            </div>
          )}
        </div>

        {/* Status */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Publication Status
          </h3>
          
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="status"
                value="draft"
                checked={formData.status === 'draft'}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                className="mr-2"
              />
              <span className="text-gray-700 dark:text-gray-300">
                Draft - Only visible to you
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="status"
                value="published"
                checked={formData.status === 'published'}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                className="mr-2"
              />
              <span className="text-gray-700 dark:text-gray-300">
                Published - Visible to everyone
              </span>
            </label>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading || usernameAvailable === false}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-md font-medium transition-colors flex items-center"
          >
            {loading && <LoadingSpinner size="sm" className="mr-2" />}
            {profile ? 'Update Profile' : 'Create Profile'}
          </button>
        </div>
      </form>
    </div>
  );
}
