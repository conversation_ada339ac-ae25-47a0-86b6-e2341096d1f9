'use client';

import { useState, useEffect } from 'react';
import { HielProfile, HielAnalytics, db } from '@/lib/supabase';
import { motion } from 'framer-motion';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface HielAnalyticsDashboardProps {
  profiles: HielProfile[];
}

export default function HielAnalyticsDashboard({ profiles }: HielAnalyticsDashboardProps) {
  const [selectedProfile, setSelectedProfile] = useState<HielProfile | null>(null);
  const [analytics, setAnalytics] = useState<HielAnalytics[]>([]);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState(30);

  useEffect(() => {
    if (profiles.length > 0 && !selectedProfile) {
      setSelectedProfile(profiles[0]);
    }
  }, [profiles]);

  useEffect(() => {
    if (selectedProfile) {
      loadAnalytics();
    }
  }, [selectedProfile, timeRange]);

  const loadAnalytics = async () => {
    if (!selectedProfile) return;

    setLoading(true);
    try {
      const data = await db.getHielAnalytics(selectedProfile.id, timeRange);
      setAnalytics(data);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAnalyticsStats = () => {
    const profileViews = analytics.filter(a => a.event_type === 'profile_view').length;
    const linkClicks = analytics.filter(a => a.event_type === 'link_click').length;
    
    // Group by date for chart data
    const dailyStats: Record<string, { views: number; clicks: number }> = {};
    
    analytics.forEach(event => {
      const date = new Date(event.created_at).toISOString().split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { views: 0, clicks: 0 };
      }
      
      if (event.event_type === 'profile_view') {
        dailyStats[date].views++;
      } else if (event.event_type === 'link_click') {
        dailyStats[date].clicks++;
      }
    });

    // Get top referrers
    const referrers: Record<string, number> = {};
    analytics.forEach(event => {
      if (event.referrer) {
        const domain = new URL(event.referrer).hostname;
        referrers[domain] = (referrers[domain] || 0) + 1;
      }
    });

    const topReferrers = Object.entries(referrers)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    return {
      profileViews,
      linkClicks,
      dailyStats,
      topReferrers,
    };
  };

  const stats = getAnalyticsStats();

  if (profiles.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📊</div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No Analytics Available
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Create a profile first to see analytics data.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Analytics Dashboard
        </h2>
        
        <div className="flex space-x-4">
          {/* Profile Selector */}
          <select
            value={selectedProfile?.id || ''}
            onChange={(e) => {
              const profile = profiles.find(p => p.id === e.target.value);
              setSelectedProfile(profile || null);
            }}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {profiles.map((profile) => (
              <option key={profile.id} value={profile.id}>
                {profile.business_name} (@{profile.username})
              </option>
            ))}
          </select>

          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          >
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className="text-2xl mr-3">👁️</div>
                <div>
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {stats.profileViews}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Profile Views
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className="text-2xl mr-3">🖱️</div>
                <div>
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {stats.linkClicks}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Link Clicks
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className="text-2xl mr-3">📈</div>
                <div>
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {stats.linkClicks > 0 ? ((stats.linkClicks / stats.profileViews) * 100).toFixed(1) : 0}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Click Rate
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center">
                <div className="text-2xl mr-3">🔗</div>
                <div>
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {selectedProfile?.links?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Active Links
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Daily Activity Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Daily Activity
            </h3>
            
            {Object.keys(stats.dailyStats).length > 0 ? (
              <div className="space-y-2">
                {Object.entries(stats.dailyStats)
                  .sort(([a], [b]) => a.localeCompare(b))
                  .slice(-14) // Show last 14 days
                  .map(([date, data]) => (
                    <div key={date} className="flex items-center space-x-4">
                      <div className="w-20 text-sm text-gray-600 dark:text-gray-400">
                        {new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                      </div>
                      <div className="flex-1 flex items-center space-x-2">
                        <div className="flex items-center space-x-1">
                          <div 
                            className="bg-blue-500 h-4 rounded"
                            style={{ width: `${Math.max(data.views * 10, 4)}px` }}
                          ></div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {data.views} views
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <div 
                            className="bg-green-500 h-4 rounded"
                            style={{ width: `${Math.max(data.clicks * 10, 4)}px` }}
                          ></div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {data.clicks} clicks
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No activity data available for the selected time range.
              </div>
            )}
          </motion.div>

          {/* Top Referrers */}
          {stats.topReferrers.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
            >
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Top Referrers
              </h3>
              
              <div className="space-y-3">
                {stats.topReferrers.map(([domain, count], index) => (
                  <div key={domain} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center text-xs font-medium">
                        {index + 1}
                      </div>
                      <span className="text-gray-900 dark:text-white">{domain}</span>
                    </div>
                    <span className="text-gray-600 dark:text-gray-400">{count} visits</span>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Link Performance */}
          {selectedProfile?.links && selectedProfile.links.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
            >
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Link Performance
              </h3>
              
              <div className="space-y-3">
                {selectedProfile.links
                  .sort((a, b) => (b.click_count || 0) - (a.click_count || 0))
                  .map((link) => (
                    <div key={link.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">
                          {link.platform === 'instagram' ? '📷' :
                           link.platform === 'facebook' ? '📘' :
                           link.platform === 'twitter' ? '🐦' :
                           link.platform === 'linkedin' ? '💼' :
                           link.platform === 'youtube' ? '📺' :
                           link.platform === 'github' ? '🐙' : '🔗'}
                        </span>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {link.title}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {link.url}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-gray-900 dark:text-white">
                          {link.click_count || 0}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          clicks
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </motion.div>
          )}
        </div>
      )}
    </div>
  );
}
