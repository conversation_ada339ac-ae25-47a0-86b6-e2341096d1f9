'use client';

import { HielProfile } from '@/lib/supabase';
import { motion } from 'framer-motion';
import { useState } from 'react';

interface HielPublicProfileProps {
  profile: HielProfile;
  onLinkClick: (linkId: string, url: string) => void;
}

export default function HielPublicProfile({ profile, onLinkClick }: HielPublicProfileProps) {
  const [imageError, setImageError] = useState(false);

  const getPlatformIcon = (platform?: string) => {
    switch (platform) {
      case 'instagram': return '📷';
      case 'facebook': return '📘';
      case 'twitter': return '🐦';
      case 'linkedin': return '💼';
      case 'youtube': return '📺';
      case 'tiktok': return '🎵';
      case 'whatsapp': return '💬';
      case 'telegram': return '✈️';
      case 'github': return '🐙';
      default: return '🔗';
    }
  };

  const formatUrl = (url: string) => {
    return url.startsWith('http') ? url : `https://${url}`;
  };

  const activeLinks = profile.links?.filter(link => link.is_active) || [];

  return (
    <div className="min-h-screen py-8 px-4">
      <div className="max-w-md mx-auto">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          {/* Background Image */}
          {profile.background_image_url && (
            <div 
              className="w-full h-32 rounded-2xl mb-6 bg-cover bg-center relative overflow-hidden"
              style={{ 
                backgroundImage: `url(${profile.background_image_url})`,
                backgroundColor: profile.theme_color 
              }}
            >
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            </div>
          )}

          {/* Profile Picture */}
          <div className="relative inline-block mb-4">
            <div 
              className="w-24 h-24 rounded-full border-4 border-white shadow-lg overflow-hidden mx-auto"
              style={{ borderColor: profile.theme_color }}
            >
              {profile.logo_url && !imageError ? (
                <img
                  src={profile.logo_url}
                  alt={profile.business_name}
                  className="w-full h-full object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div 
                  className="w-full h-full flex items-center justify-center text-2xl font-bold text-white"
                  style={{ backgroundColor: profile.theme_color }}
                >
                  {profile.business_name.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
          </div>

          {/* Business Name */}
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {profile.business_name}
          </h1>

          {/* Username */}
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            @{profile.username}
          </p>

          {/* Description */}
          {profile.description && (
            <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
              {profile.description}
            </p>
          )}

          {/* Contact Info */}
          <div className="flex flex-wrap justify-center gap-4 mb-6 text-sm">
            {profile.location && (
              <div className="flex items-center text-gray-600 dark:text-gray-400">
                <span className="mr-1">📍</span>
                {profile.location}
              </div>
            )}
            {profile.email && (
              <a
                href={`mailto:${profile.email}`}
                className="flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                <span className="mr-1">✉️</span>
                {profile.email}
              </a>
            )}
            {profile.phone && (
              <a
                href={`tel:${profile.phone}`}
                className="flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              >
                <span className="mr-1">📞</span>
                {profile.phone}
              </a>
            )}
          </div>

          {/* Website Link */}
          {profile.website && (
            <motion.a
              href={formatUrl(profile.website)}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-6 py-3 rounded-full text-white font-medium transition-all duration-300 hover:scale-105 shadow-lg mb-6"
              style={{ backgroundColor: profile.theme_color }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="mr-2">🌐</span>
              Visit Website
            </motion.a>
          )}
        </motion.div>

        {/* Links Section */}
        <div className="space-y-4">
          {activeLinks.map((link, index) => (
            <motion.button
              key={link.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onLinkClick(link.id, formatUrl(link.url))}
              className="w-full p-4 bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300 group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center space-x-4">
                {/* Icon */}
                <div 
                  className="w-12 h-12 rounded-xl flex items-center justify-center text-xl"
                  style={{ backgroundColor: profile.theme_color + '20' }}
                >
                  {getPlatformIcon(link.platform)}
                </div>

                {/* Content */}
                <div className="flex-1 text-left">
                  <div className="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {link.title}
                  </div>
                  {link.description && (
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {link.description}
                    </div>
                  )}
                </div>

                {/* Arrow */}
                <div className="text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </motion.button>
          ))}

          {activeLinks.length === 0 && (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">🔗</div>
              <p className="text-gray-600 dark:text-gray-400">
                No links available yet.
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-12 pt-8 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-center space-x-2 text-gray-500 dark:text-gray-400 text-sm">
            <span>Powered by</span>
            <a
              href="/"
              className="font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
            >
              HielTech
            </a>
          </div>
        </motion.div>

        {/* Share Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="fixed bottom-6 right-6"
        >
          <button
            onClick={() => {
              if (navigator.share) {
                navigator.share({
                  title: profile.business_name,
                  text: profile.description || `Check out ${profile.business_name}'s links`,
                  url: window.location.href,
                });
              } else {
                navigator.clipboard.writeText(window.location.href);
                alert('Link copied to clipboard!');
              }
            }}
            className="w-12 h-12 rounded-full shadow-lg flex items-center justify-center text-white transition-all duration-300 hover:scale-110"
            style={{ backgroundColor: profile.theme_color }}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
          </button>
        </motion.div>
      </div>
    </div>
  );
}
