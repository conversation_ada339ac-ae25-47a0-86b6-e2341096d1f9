'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, HielProfile, HielSettings, Profile, supabase } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type TabType = 'overview' | 'profiles' | 'settings' | 'analytics';

export default function HielLinksManagement() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalProfiles: 0,
    publishedProfiles: 0,
    totalUsers: 0,
    totalViews: 0,
    totalClicks: 0,
  });

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      
      // Get all profiles for stats
      const { data: profiles, error: profilesError } = await supabase
        .from('hiel_profiles')
        .select('*');

      if (profilesError) throw profilesError;

      // Get user count
      const { data: users, error: usersError } = await supabase
        .from('profiles')
        .select('id');

      if (usersError) throw usersError;

      const totalProfiles = profiles?.length || 0;
      const publishedProfiles = profiles?.filter(p => p.status === 'published').length || 0;
      const totalViews = profiles?.reduce((sum, p) => sum + (p.view_count || 0), 0) || 0;
      const totalClicks = profiles?.reduce((sum, p) => sum + (p.click_count || 0), 0) || 0;

      setStats({
        totalProfiles,
        publishedProfiles,
        totalUsers: users?.length || 0,
        totalViews,
        totalClicks,
      });
    } catch (error) {
      console.error('Error loading HielLinks stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'profiles', label: 'Profiles', icon: '👤' },
    { id: 'settings', label: 'User Settings', icon: '⚙️' },
    { id: 'analytics', label: 'Analytics', icon: '📈' },
  ] as const;

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          HielLinks Management
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Manage HielLinks profiles, user settings, and analytics
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <span className="text-2xl">🔗</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Profiles
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.totalProfiles}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <span className="text-2xl">✅</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Published
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.publishedProfiles}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <span className="text-2xl">👥</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Users
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.totalUsers}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
              <span className="text-2xl">👁️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Views
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.totalViews.toLocaleString()}
              </p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <span className="text-2xl">🖱️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Clicks
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {stats.totalClicks.toLocaleString()}
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <HielOverviewTab stats={stats} onRefresh={loadStats} />
          )}
          {activeTab === 'profiles' && (
            <HielProfilesTab onRefresh={loadStats} />
          )}
          {activeTab === 'settings' && (
            <HielUserSettingsTab onRefresh={loadStats} />
          )}
          {activeTab === 'analytics' && (
            <HielAnalyticsTab />
          )}
        </div>
      </div>
    </div>
  );
}

function HielOverviewTab({ stats, onRefresh }: { stats: any; onRefresh: () => void }) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h3>
          <div className="space-y-3">
            <button className="w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-white dark:hover:bg-gray-600 transition-colors">
              <div className="flex items-center">
                <span className="text-xl mr-3">👤</span>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    Manage Profiles
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    View and moderate user profiles
                  </p>
                </div>
              </div>
            </button>

            <button className="w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-white dark:hover:bg-gray-600 transition-colors">
              <div className="flex items-center">
                <span className="text-xl mr-3">⚙️</span>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    User Settings
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Configure user limits and permissions
                  </p>
                </div>
              </div>
            </button>

            <button className="w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-white dark:hover:bg-gray-600 transition-colors">
              <div className="flex items-center">
                <span className="text-xl mr-3">📈</span>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    View Analytics
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Check service performance metrics
                  </p>
                </div>
              </div>
            </button>
          </div>
        </div>

        {/* Service Health */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Service Health
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">Database</span>
              <span className="flex items-center text-green-600 dark:text-green-400">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Healthy
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">Analytics</span>
              <span className="flex items-center text-green-600 dark:text-green-400">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Active
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">Public Pages</span>
              <span className="flex items-center text-green-600 dark:text-green-400">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Online
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Recent Activity
        </h3>
        <div className="space-y-3">
          <div className="flex items-center text-sm">
            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            <span className="text-gray-600 dark:text-gray-400">
              HielLinks service initialized and ready
            </span>
            <span className="ml-auto text-gray-500 dark:text-gray-500">
              {new Date().toLocaleTimeString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

function HielProfilesTab({ onRefresh }: { onRefresh: () => void }) {
  const [profiles, setProfiles] = useState<HielProfile[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfiles();
  }, []);

  const loadProfiles = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('hiel_profiles')
        .select(`
          *,
          user:profiles(id, display_name, email),
          links:hiel_links(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProfiles(data || []);
    } catch (error) {
      console.error('Error loading profiles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (profileId: string, status: string) => {
    try {
      await db.updateHielProfile(profileId, { status: status as any });
      loadProfiles();
      onRefresh();
    } catch (error) {
      console.error('Error updating profile status:', error);
      alert('Failed to update profile status');
    }
  };

  const handleDeleteProfile = async (profileId: string) => {
    if (!confirm('Are you sure you want to delete this profile? This action cannot be undone.')) {
      return;
    }

    try {
      await db.deleteHielProfile(profileId);
      loadProfiles();
      onRefresh();
    } catch (error) {
      console.error('Error deleting profile:', error);
      alert('Failed to delete profile');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          All HielLinks Profiles ({profiles.length})
        </h3>
        <button
          onClick={loadProfiles}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
        >
          Refresh
        </button>
      </div>

      {profiles.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔗</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No profiles found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Users haven't created any HielLinks profiles yet.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {profiles.map((profile) => (
            <div
              key={profile.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div
                    className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                    style={{ backgroundColor: profile.theme_color }}
                  >
                    {profile.logo_url ? (
                      <img
                        src={profile.logo_url}
                        alt={profile.business_name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      profile.business_name.charAt(0).toUpperCase()
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      {profile.business_name}
                    </h4>
                    <p className="text-sm text-blue-600 dark:text-blue-400">
                      @{profile.username}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {profile.user?.display_name} ({profile.user?.email})
                    </p>
                    {profile.description && (
                      <p className="text-sm text-gray-700 dark:text-gray-300 mt-2">
                        {profile.description}
                      </p>
                    )}
                    <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500 dark:text-gray-400">
                      <span>{profile.view_count} views</span>
                      <span>{profile.click_count} clicks</span>
                      <span>{profile.links?.length || 0} links</span>
                      <span>Created {new Date(profile.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <select
                    value={profile.status}
                    onChange={(e) => handleStatusChange(profile.id, e.target.value)}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                    <option value="suspended">Suspended</option>
                  </select>
                  <a
                    href={`/hielLinks/${profile.username}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-md text-sm font-medium transition-colors"
                  >
                    View
                  </a>
                  <button
                    onClick={() => handleDeleteProfile(profile.id)}
                    className="bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 px-3 py-1 rounded-md text-sm font-medium transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function HielUserSettingsTab({ onRefresh }: { onRefresh: () => void }) {
  return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">⚙️</div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        User Settings Management
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        User settings management will be implemented in a future update.
      </p>
    </div>
  );
}

function HielAnalyticsTab() {
  return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">📈</div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Advanced Analytics
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Advanced analytics dashboard will be implemented in a future update.
      </p>
    </div>
  );
}
