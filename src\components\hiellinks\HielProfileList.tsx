'use client';

import { HielProfile } from '@/lib/supabase';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface HielProfileListProps {
  profiles: HielProfile[];
  canCreateProfile: boolean;
  onCreateProfile: () => void;
  onEditProfile: (profile: HielProfile) => void;
  onDeleteProfile: (profileId: string) => void;
}

export default function HielProfileList({
  profiles,
  canCreateProfile,
  onCreateProfile,
  onEditProfile,
  onDeleteProfile,
}: HielProfileListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'suspended':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Your HielLinks Profiles
        </h2>
        {canCreateProfile && (
          <button
            onClick={onCreateProfile}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Create New Profile
          </button>
        )}
      </div>

      {/* Profiles Grid */}
      {profiles.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔗</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No profiles yet
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Create your first HielLinks profile to get started
          </p>
          {canCreateProfile && (
            <button
              onClick={onCreateProfile}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Create Your First Profile
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {profiles.map((profile, index) => (
            <motion.div
              key={profile.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              {/* Profile Header */}
              <div 
                className="h-20 relative"
                style={{ backgroundColor: profile.theme_color }}
              >
                {profile.background_image_url && (
                  <img
                    src={profile.background_image_url}
                    alt=""
                    className="w-full h-full object-cover"
                  />
                )}
                <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                
                {/* Logo */}
                <div className="absolute -bottom-6 left-4">
                  <div className="w-12 h-12 rounded-full bg-white dark:bg-gray-800 border-2 border-white dark:border-gray-700 flex items-center justify-center overflow-hidden">
                    {profile.logo_url ? (
                      <img
                        src={profile.logo_url}
                        alt={profile.business_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-lg font-bold text-gray-600 dark:text-gray-400">
                        {profile.business_name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                </div>

                {/* Status Badge */}
                <div className="absolute top-3 right-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(profile.status)}`}>
                    {profile.status.charAt(0).toUpperCase() + profile.status.slice(1)}
                  </span>
                </div>
              </div>

              {/* Profile Content */}
              <div className="p-4 pt-8">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  {profile.business_name}
                </h3>
                <p className="text-sm text-blue-600 dark:text-blue-400 mb-2">
                  @{profile.username}
                </p>
                {profile.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                    {profile.description}
                  </p>
                )}

                {/* Stats */}
                <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <span>{profile.view_count} views</span>
                  <span>{profile.click_count} clicks</span>
                  <span>{profile.links?.length || 0} links</span>
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => onEditProfile(profile)}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    Edit
                  </button>
                  {profile.status === 'published' && (
                    <Link
                      href={`/hielLinks/${profile.username}`}
                      target="_blank"
                      className="flex-1 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 px-3 py-2 rounded-md text-sm font-medium transition-colors text-center"
                    >
                      View
                    </Link>
                  )}
                  <button
                    onClick={() => onDeleteProfile(profile.id)}
                    className="bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    Delete
                  </button>
                </div>

                {/* Created Date */}
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Created {formatDate(profile.created_at)}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Limit Warning */}
      {!canCreateProfile && (
        <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-center">
            <div className="text-yellow-600 dark:text-yellow-400 mr-3">⚠️</div>
            <div>
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200">
                Profile Limit Reached
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                You've reached your maximum number of profiles. Upgrade your plan to create more profiles.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
