'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { db, HielProfile, HielSettings } from '@/lib/supabase';
import { motion } from 'framer-motion';
import HielProfileEditor from '@/components/hiellinks/HielProfileEditor';
import HielProfileList from '@/components/hiellinks/HielProfileList';
import HielAnalyticsDashboard from '@/components/hiellinks/HielAnalyticsDashboard';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type TabType = 'profiles' | 'editor' | 'analytics';

export default function HielLinksPage() {
  const { user, profile, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('profiles');
  const [profiles, setProfiles] = useState<HielProfile[]>([]);
  const [settings, setSettings] = useState<HielSettings | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<HielProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user && !authLoading) {
      loadData();
    }
  }, [user, authLoading]);

  const loadData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [userProfiles, userSettings] = await Promise.all([
        db.getUserHielProfiles(user.id),
        db.getHielSettings(user.id)
      ]);

      setProfiles(userProfiles);
      setSettings(userSettings);
    } catch (error) {
      console.error('Error loading HielLinks data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProfile = () => {
    setSelectedProfile(null);
    setActiveTab('editor');
  };

  const handleEditProfile = (profile: HielProfile) => {
    setSelectedProfile(profile);
    setActiveTab('editor');
  };

  const handleProfileSaved = () => {
    loadData();
    setActiveTab('profiles');
    setSelectedProfile(null);
  };

  const handleDeleteProfile = async (profileId: string) => {
    if (!confirm('Are you sure you want to delete this profile? This action cannot be undone.')) {
      return;
    }

    try {
      await db.deleteHielProfile(profileId);
      loadData();
    } catch (error) {
      console.error('Error deleting profile:', error);
      alert('Failed to delete profile. Please try again.');
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Please log in to access HielLinks.
          </p>
        </div>
      </div>
    );
  }

  const canCreateProfile = !settings || profiles.length < settings.max_profiles;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            HielLinks
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Create and manage your business profile pages
          </p>
        </div>

        {/* Usage Stats */}
        {settings && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Usage Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {profiles.length}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Profiles ({settings.max_profiles} max)
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {profiles.reduce((sum, p) => sum + p.view_count, 0)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Total Views
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {profiles.reduce((sum, p) => sum + p.click_count, 0)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Total Clicks
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'profiles', label: 'My Profiles', icon: '👤' },
                { id: 'editor', label: 'Profile Editor', icon: '✏️' },
                { id: 'analytics', label: 'Analytics', icon: '📊' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as TabType)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'profiles' && (
              <HielProfileList
                profiles={profiles}
                canCreateProfile={canCreateProfile}
                onCreateProfile={handleCreateProfile}
                onEditProfile={handleEditProfile}
                onDeleteProfile={handleDeleteProfile}
              />
            )}

            {activeTab === 'editor' && (
              <HielProfileEditor
                profile={selectedProfile}
                settings={settings}
                onSave={handleProfileSaved}
                onCancel={() => setActiveTab('profiles')}
              />
            )}

            {activeTab === 'analytics' && (
              <HielAnalyticsDashboard
                profiles={profiles}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
