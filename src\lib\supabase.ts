import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    storageKey: 'supabase.auth.token',
    flowType: 'pkce'
  }
});

// Database types
export interface Profile {
  id: string;
  email: string;
  display_name?: string;
  bio?: string;
  company?: string;
  position?: string;
  role: 'user' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  due_date?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Inquiry {
  id: string;
  type: 'contact' | 'service';
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
  service_type?: string;
  budget_range?: string;
  timeline?: string;
  status: 'new' | 'read' | 'responded' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  admin_notes?: string;
  created_at: string;
  updated_at: string;
  read_at?: string;
  responded_at?: string;
}

// Blog Management Types
export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogTag {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  usage_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogMedia {
  id: string;
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  width?: number;
  height?: number;
  alt_text?: string;
  caption?: string;
  uploaded_by?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featured_image_id?: string;
  featured_image?: BlogMedia;
  category_id?: string;
  category?: BlogCategory;
  author_id?: string;
  author?: Profile;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  visibility: 'public' | 'private' | 'password';
  password?: string;
  published_at?: string;
  scheduled_at?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image_id?: string;
  og_image?: BlogMedia;
  reading_time: number;
  view_count: number;
  like_count: number;
  comment_count: number;
  is_featured: boolean;
  allow_comments: boolean;
  template: string;
  custom_css?: string;
  custom_js?: string;
  external_links?: {
    github?: string;
    demo?: string;
    playstore?: string;
    appstore?: string;
    youtube?: string;
    website?: string;
    [key: string]: string | undefined;
  };
  tags?: BlogTag[];
  created_at: string;
  updated_at: string;
}

export interface BlogPostTag {
  id: string;
  post_id: string;
  tag_id: string;
  created_at: string;
}

export interface BlogPostAnalytics {
  id: string;
  post_id: string;
  date: string;
  views: number;
  unique_views: number;
  likes: number;
  shares: number;
  comments: number;
  bounce_rate?: number;
  avg_time_on_page?: number;
  referrer_data?: Record<string, unknown>;
  device_data?: Record<string, unknown>;
  location_data?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio?: string;
  email?: string;
  phone?: string;
  avatar_url?: string;
  linkedin_url?: string;
  github_url?: string;
  twitter_url?: string;
  website_url?: string;
  skills?: string[];
  is_active: boolean;
  is_featured: boolean;
  display_order: number;
  joined_date: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

export interface TeamApplication {
  id: string;
  name: string;
  email: string;
  phone?: string;
  desired_role: string;
  experience_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  motivation: string;
  skills?: string[];
  portfolio_url?: string;
  linkedin_url?: string;
  github_url?: string;
  resume_url?: string;
  availability: 'part-time' | 'full-time' | 'volunteer' | 'flexible';
  location?: string;
  timezone?: string;
  previous_nonprofit_experience?: string;
  additional_info?: string;
  status: 'pending' | 'reviewing' | 'approved' | 'rejected' | 'withdrawn';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  admin_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  account_created: boolean;
  account_created_at?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

// Chat System Types
export interface ChatPermission {
  id: string;
  user_id: string;
  application_id: string;
  is_enabled: boolean;
  enabled_by?: string;
  enabled_at?: string;
  disabled_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ChatRoom {
  id: string;
  type: 'applicant_admin' | 'admin_only';
  name?: string;
  application_id?: string;
  created_by?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  participants?: ChatRoomParticipant[];
  last_message?: ChatMessage;
  unread_count?: number;
}

export interface ChatMessage {
  id: string;
  room_id: string;
  sender_id: string;
  sender?: Profile;
  message: string;
  message_type: 'text' | 'file' | 'system';
  file_url?: string;
  is_edited: boolean;
  edited_at?: string;
  created_at: string;
}

export interface ChatRoomParticipant {
  id: string;
  room_id: string;
  user_id: string;
  user?: Profile;
  joined_at: string;
  last_read_at: string;
}

export interface BlogComment {
  id: string;
  post_id: string;
  parent_id?: string;
  author_name: string;
  author_email: string;
  author_website?: string;
  content: string;
  status: 'pending' | 'approved' | 'spam' | 'trash';
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  updated_at: string;
}

// HielLinks Service Types
export interface HielProfile {
  id: string;
  user_id: string;
  username: string;
  business_name: string;
  description?: string;
  logo_url?: string;
  background_image_url?: string;
  theme_color: string;
  text_color: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  email?: string;
  website?: string;
  is_active: boolean;
  is_featured: boolean;
  view_count: number;
  click_count: number;
  status: 'draft' | 'published' | 'suspended';
  created_at: string;
  updated_at: string;
  user?: Profile;
  links?: HielLink[];
}

export interface HielLink {
  id: string;
  profile_id: string;
  title: string;
  url: string;
  type: 'website' | 'social' | 'contact' | 'custom';
  platform?: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'whatsapp' | 'telegram' | 'github' | 'other';
  icon?: string;
  description?: string;
  is_active: boolean;
  click_count: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface HielAnalytics {
  id: string;
  profile_id: string;
  link_id?: string;
  event_type: 'profile_view' | 'link_click';
  visitor_ip?: string;
  user_agent?: string;
  referrer?: string;
  country?: string;
  city?: string;
  device_type?: string;
  browser?: string;
  created_at: string;
}

export interface HielSettings {
  id: string;
  user_id: string;
  max_profiles: number;
  max_links_per_profile: number;
  max_storage_mb: number;
  can_use_custom_domain: boolean;
  can_use_analytics: boolean;
  can_remove_branding: boolean;
  subscription_type: 'free' | 'premium' | 'enterprise';
  subscription_expires_at?: string;
  created_at: string;
  updated_at: string;
}

// Blog Management Form Types
export interface BlogPostFormData {
  title: string;
  slug?: string;
  excerpt?: string;
  content: string;
  featured_image_id?: string;
  category_id?: string;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  visibility: 'public' | 'private' | 'password';
  password?: string;
  published_at?: string;
  scheduled_at?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image_id?: string;
  is_featured: boolean;
  allow_comments: boolean;
  template: string;
  custom_css?: string;
  custom_js?: string;
  external_links?: {
    github?: string;
    demo?: string;
    playstore?: string;
    appstore?: string;
    youtube?: string;
    website?: string;
    [key: string]: string | undefined;
  };
  tag_ids?: string[];
}

export interface BlogCategoryFormData {
  name: string;
  slug?: string;
  description?: string;
  color: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
}

export interface BlogTagFormData {
  name: string;
  slug?: string;
  description?: string;
  color: string;
  is_active: boolean;
}

// Database helper functions
export const db = {
  // Profile operations
  async getProfile(userId: string): Promise<Profile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          // No rows returned - profile doesn't exist
          console.log('Profile not found for user:', userId);
          return null;
        }

        console.error('Error fetching profile:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId: userId
        });
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error fetching profile:', error);
      return null;
    }
  },

  async createProfile(userId: string, email: string, displayName?: string): Promise<Profile | null> {
    try {
      const profileData = {
        id: userId,
        email: email,
        display_name: displayName || email.split('@')[0],
        role: email === '<EMAIL>' ? 'admin' as const : 'user' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('profiles')
        .insert([profileData])
        .select()
        .single();

      if (error) {
        console.error('Error creating profile:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId: userId,
          email: email
        });
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error creating profile:', error);
      return null;
    }
  },

  async updateProfile(userId: string, updates: Partial<Profile>): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating profile:', error);
      return null;
    }
    
    return data;
  },

  // Task operations
  async getTasks(userId: string): Promise<Task[]> {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('created_by', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching tasks:', error);
      return [];
    }
    
    return data || [];
  },

  async createTask(task: Omit<Task, 'id' | 'created_at' | 'updated_at'>): Promise<Task | null> {
    const { data, error } = await supabase
      .from('tasks')
      .insert([task])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating task:', error);
      return null;
    }
    
    return data;
  },

  async updateTask(taskId: string, updates: Partial<Task>): Promise<Task | null> {
    const { data, error } = await supabase
      .from('tasks')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', taskId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating task:', error);
      return null;
    }
    
    return data;
  },

  async deleteTask(taskId: string): Promise<boolean> {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId);
    
    if (error) {
      console.error('Error deleting task:', error);
      return false;
    }
    
    return true;
  },

  // Inquiry operations
  async getInquiries(): Promise<Inquiry[]> {
    const { data, error } = await supabase
      .from('inquiries')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching inquiries:', error);
      return [];
    }

    return data || [];
  },

  async createInquiry(inquiry: Omit<Inquiry, 'id' | 'created_at' | 'updated_at' | 'status' | 'priority' | 'read_at' | 'responded_at'>): Promise<Inquiry | null> {
    const { data, error } = await supabase
      .from('inquiries')
      .insert([inquiry])
      .select()
      .single();

    if (error) {
      console.error('Error creating inquiry:', error);
      return null;
    }

    return data;
  },

  async updateInquiry(inquiryId: string, updates: Partial<Inquiry>): Promise<Inquiry | null> {
    const updateData: Partial<Inquiry> & { read_at?: string; responded_at?: string } = { ...updates };

    // Set read_at timestamp if status is being changed to 'read'
    if (updates.status === 'read' && !updates.read_at) {
      updateData.read_at = new Date().toISOString();
    }

    // Set responded_at timestamp if status is being changed to 'responded'
    if (updates.status === 'responded' && !updates.responded_at) {
      updateData.responded_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('inquiries')
      .update(updateData)
      .eq('id', inquiryId)
      .select()
      .single();

    if (error) {
      console.error('Error updating inquiry:', error);
      return null;
    }

    return data;
  },

  async deleteInquiry(inquiryId: string): Promise<boolean> {
    const { error } = await supabase
      .from('inquiries')
      .delete()
      .eq('id', inquiryId);

    if (error) {
      console.error('Error deleting inquiry:', error);
      return false;
    }

    return true;
  },

  // Admin helper functions
  async isUserAdmin(userId: string): Promise<boolean> {
    const profile = await this.getProfile(userId);
    return profile?.role === 'admin' || profile?.email === '<EMAIL>';
  },

  // Blog Categories operations
  async getCategories(): Promise<BlogCategory[]> {
    const { data, error } = await supabase
      .from('blog_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Error fetching categories:', error);
      return [];
    }

    return data || [];
  },

  async getCategoryBySlug(slug: string): Promise<BlogCategory | null> {
    const { data, error } = await supabase
      .from('blog_categories')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return null;
    }

    return data;
  },

  async createCategory(categoryData: BlogCategoryFormData): Promise<BlogCategory | null> {
    const { data, error } = await supabase
      .from('blog_categories')
      .insert([categoryData])
      .select()
      .single();

    if (error) {
      console.error('Error creating category:', error);
      return null;
    }

    return data;
  },

  async updateCategory(categoryId: string, updates: Partial<BlogCategoryFormData>): Promise<BlogCategory | null> {
    const { data, error } = await supabase
      .from('blog_categories')
      .update(updates)
      .eq('id', categoryId)
      .select()
      .single();

    if (error) {
      console.error('Error updating category:', error);
      return null;
    }

    return data;
  },

  async deleteCategory(categoryId: string): Promise<boolean> {
    const { error } = await supabase
      .from('blog_categories')
      .delete()
      .eq('id', categoryId);

    if (error) {
      console.error('Error deleting category:', error);
      return false;
    }

    return true;
  },

  // Blog Tags operations
  async getTags(): Promise<BlogTag[]> {
    const { data, error } = await supabase
      .from('blog_tags')
      .select('*')
      .eq('is_active', true)
      .order('usage_count', { ascending: false });

    if (error) {
      console.error('Error fetching tags:', error);
      return [];
    }

    return data || [];
  },

  async getTagBySlug(slug: string): Promise<BlogTag | null> {
    const { data, error } = await supabase
      .from('blog_tags')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching tag:', error);
      return null;
    }

    return data;
  },

  async createTag(tagData: BlogTagFormData): Promise<BlogTag | null> {
    const { data, error } = await supabase
      .from('blog_tags')
      .insert([tagData])
      .select()
      .single();

    if (error) {
      console.error('Error creating tag:', error);
      return null;
    }

    return data;
  },

  async updateTag(tagId: string, updates: Partial<BlogTagFormData>): Promise<BlogTag | null> {
    const { data, error } = await supabase
      .from('blog_tags')
      .update(updates)
      .eq('id', tagId)
      .select()
      .single();

    if (error) {
      console.error('Error updating tag:', error);
      return null;
    }

    return data;
  },

  async deleteTag(tagId: string): Promise<boolean> {
    const { error } = await supabase
      .from('blog_tags')
      .delete()
      .eq('id', tagId);

    if (error) {
      console.error('Error deleting tag:', error);
      return false;
    }

    return true;
  },

  // Blog Posts operations
  async getBlogPosts(options?: {
    status?: string;
    category?: string;
    tag?: string;
    featured?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<BlogPost[]> {
    let query = supabase
      .from('blog_posts')
      .select(`
        *,
        category:blog_categories(*),
        author:profiles(*),
        featured_image:blog_media!blog_posts_featured_image_id_fkey(*),
        og_image:blog_media!blog_posts_og_image_id_fkey(*),
        tags:blog_post_tags(blog_tags(*))
      `);

    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.category) {
      query = query.eq('category_id', options.category);
    }

    if (options?.featured !== undefined) {
      query = query.eq('is_featured', options.featured);
    }

    query = query.order('created_at', { ascending: false });

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching blog posts:', error);
      return [];
    }

    // Transform the data to include tags properly
    return (data || []).map(post => ({
      ...post,
      tags: post.tags?.map((pt: { blog_tags: BlogTag }) => pt.blog_tags) || []
    }));
  },

  async getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select(`
        *,
        category:blog_categories(*),
        author:profiles(*),
        featured_image:blog_media!blog_posts_featured_image_id_fkey(*),
        og_image:blog_media!blog_posts_og_image_id_fkey(*),
        tags:blog_post_tags(blog_tags(*))
      `)
      .eq('slug', slug)
      .single();

    if (error) {
      console.error('Error fetching blog post:', error);
      return null;
    }

    // Transform the data to include tags properly
    return {
      ...data,
      tags: data.tags?.map((pt: { blog_tags: BlogTag }) => pt.blog_tags) || []
    };
  },

  async createBlogPost(postData: BlogPostFormData): Promise<BlogPost | null> {
    const { tag_ids, ...postFields } = postData;

    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .insert([postFields])
      .select()
      .single();

    if (postError) {
      console.error('Error creating blog post:', postError);
      return null;
    }

    // Add tags if provided
    if (tag_ids && tag_ids.length > 0) {
      const tagRelations = tag_ids.map(tagId => ({
        post_id: post.id,
        tag_id: tagId
      }));

      const { error: tagError } = await supabase
        .from('blog_post_tags')
        .insert(tagRelations);

      if (tagError) {
        console.error('Error adding tags to post:', tagError);
      }
    }

    return this.getBlogPostBySlug(post.slug);
  },

  // HielLinks Service Methods
  async getHielProfile(username: string): Promise<HielProfile | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_profiles')
        .select(`
          *,
          user:profiles(id, display_name, email),
          links:hiel_links(*)
        `)
        .eq('username', username)
        .eq('status', 'published')
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching HielProfile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error fetching HielProfile:', error);
      return null;
    }
  },

  async getUserHielProfiles(userId: string): Promise<HielProfile[]> {
    try {
      const { data, error } = await supabase
        .from('hiel_profiles')
        .select(`
          *,
          links:hiel_links(*)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching user HielProfiles:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching user HielProfiles:', error);
      return [];
    }
  },

  async createHielProfile(profileData: Partial<HielProfile>): Promise<HielProfile | null> {
    try {
      console.log('Creating HielProfile with data:', profileData);

      // First, try a simple insert
      const { data: insertData, error: insertError } = await supabase
        .from('hiel_profiles')
        .insert([profileData])
        .select()
        .single();

      if (insertError) {
        console.error('Error creating HielProfile:', insertError);
        console.error('Error details:', JSON.stringify(insertError, null, 2));
        console.error('Error code:', insertError.code);
        console.error('Error message:', insertError.message);
        return null;
      }

      console.log('Successfully created HielProfile:', insertData);

      // Now fetch with relations
      const { data: fullData, error: fetchError } = await supabase
        .from('hiel_profiles')
        .select(`
          *,
          user:profiles(id, display_name, email),
          links:hiel_links(*)
        `)
        .eq('id', insertData.id)
        .single();

      if (fetchError) {
        console.error('Error fetching full profile data:', fetchError);
        // Return the basic data if we can't fetch relations
        return insertData as HielProfile;
      }

      return fullData;
    } catch (error) {
      console.error('Unexpected error creating HielProfile:', error);
      return null;
    }
  },

  async updateHielProfile(id: string, updates: Partial<HielProfile>): Promise<HielProfile | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_profiles')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          user:profiles(id, display_name, email),
          links:hiel_links(*)
        `)
        .single();

      if (error) {
        console.error('Error updating HielProfile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error updating HielProfile:', error);
      return null;
    }
  },

  async deleteHielProfile(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('hiel_profiles')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting HielProfile:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error deleting HielProfile:', error);
      return false;
    }
  },

  async createHielLink(linkData: Partial<HielLink>): Promise<HielLink | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_links')
        .insert([linkData])
        .select()
        .single();

      if (error) {
        console.error('Error creating HielLink:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error creating HielLink:', error);
      return null;
    }
  },

  async updateHielLink(id: string, updates: Partial<HielLink>): Promise<HielLink | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_links')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating HielLink:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error updating HielLink:', error);
      return null;
    }
  },

  async deleteHielLink(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('hiel_links')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting HielLink:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error deleting HielLink:', error);
      return false;
    }
  },

  async trackHielAnalytics(analyticsData: Partial<HielAnalytics>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('hiel_analytics')
        .insert([analyticsData]);

      if (error) {
        console.error('Error tracking HielAnalytics:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error tracking HielAnalytics:', error);
      return false;
    }
  },

  async getHielAnalytics(profileId: string, days: number = 30): Promise<HielAnalytics[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('hiel_analytics')
        .select('*')
        .eq('profile_id', profileId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching HielAnalytics:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching HielAnalytics:', error);
      return [];
    }
  },

  async getHielSettings(userId: string): Promise<HielSettings | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching HielSettings:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error fetching HielSettings:', error);
      return null;
    }
  },

  async updateHielSettings(userId: string, updates: Partial<HielSettings>): Promise<HielSettings | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_settings')
        .update(updates)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating HielSettings:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error updating HielSettings:', error);
      return null;
    }
  },

  async incrementProfileView(profileId: string): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('increment_profile_view', {
        profile_id: profileId
      });

      if (error) {
        console.error('Error incrementing profile view:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error incrementing profile view:', error);
      return false;
    }
  },

  async incrementLinkClick(linkId: string): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('increment_link_click', {
        link_id: linkId
      });

      if (error) {
        console.error('Error incrementing link click:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error incrementing link click:', error);
      return false;
    }
  },

  async checkUsernameAvailability(username: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('hiel_profiles')
        .select('id')
        .eq('username', username)
        .single();

      if (error && error.code === 'PGRST116') {
        // No rows returned, username is available
        return true;
      }

      if (error) {
        console.error('Error checking username availability:', error);
        return false;
      }

      // Username exists
      return false;
    } catch (error) {
      console.error('Unexpected error checking username availability:', error);
      return false;
    }
  },

  async getFeaturedHielProfiles(limit: number = 10): Promise<HielProfile[]> {
    try {
      const { data, error } = await supabase
        .from('hiel_profiles')
        .select(`
          *,
          user:profiles(id, display_name),
          links:hiel_links(*)
        `)
        .eq('status', 'published')
        .eq('is_active', true)
        .eq('is_featured', true)
        .order('view_count', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching featured HielProfiles:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching featured HielProfiles:', error);
      return [];
    }
  },

  async updateBlogPost(postId: string, updates: Partial<BlogPostFormData>): Promise<BlogPost | null> {
    const { tag_ids, ...postFields } = updates;

    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .update(postFields)
      .eq('id', postId)
      .select()
      .single();

    if (postError) {
      console.error('Error updating blog post:', postError);
      return null;
    }

    // Update tags if provided
    if (tag_ids !== undefined) {
      // Remove existing tags
      await supabase
        .from('blog_post_tags')
        .delete()
        .eq('post_id', postId);

      // Add new tags
      if (tag_ids.length > 0) {
        const tagRelations = tag_ids.map(tagId => ({
          post_id: postId,
          tag_id: tagId
        }));

        const { error: tagError } = await supabase
          .from('blog_post_tags')
          .insert(tagRelations);

        if (tagError) {
          console.error('Error updating tags for post:', tagError);
        }
      }
    }

    return this.getBlogPostBySlug(post.slug);
  },

  async deleteBlogPost(postId: string): Promise<boolean> {
    const { error } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', postId);

    if (error) {
      console.error('Error deleting blog post:', error);
      return false;
    }

    return true;
  },

  async incrementPostViewCount(postId: string): Promise<void> {
    try {
      // Get current view count
      const { data: post, error: fetchError } = await supabase
        .from('blog_posts')
        .select('view_count')
        .eq('id', postId)
        .single();

      if (fetchError || !post) {
        console.error('Error fetching post for view count:', fetchError);
        return;
      }

      // Increment view count
      const { error: updateError } = await supabase
        .from('blog_posts')
        .update({ view_count: (post.view_count || 0) + 1 })
        .eq('id', postId);

      if (updateError) {
        console.error('Error incrementing post view count:', updateError);
      }
    } catch (error) {
      console.error('Error incrementing post view count:', error);
    }
  },

  // Blog Media operations
  async getMedia(): Promise<BlogMedia[]> {
    const { data, error } = await supabase
      .from('blog_media')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching media:', error);
      return [];
    }

    return data || [];
  },

  async getMediaById(mediaId: string): Promise<BlogMedia | null> {
    const { data, error } = await supabase
      .from('blog_media')
      .select('*')
      .eq('id', mediaId)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching media:', error);
      return null;
    }

    return data;
  },

  async uploadMedia(file: File, metadata?: Partial<BlogMedia>): Promise<BlogMedia | null> {
    try {
      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `blog-media/${fileName}`;

      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('blog-media')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('blog-media')
        .getPublicUrl(filePath);

      // Get image dimensions if it's an image
      let width: number | null = null;
      let height: number | null = null;

      if (file.type.startsWith('image/')) {
        const dimensions = await new Promise<{ width: number; height: number }>((resolve) => {
          const img = new Image();
          img.onload = () => resolve({ width: img.width, height: img.height });
          img.src = URL.createObjectURL(file);
        });
        width = dimensions.width;
        height = dimensions.height;
      }

      // Save media record to database
      const mediaData = {
        filename: fileName,
        original_name: file.name,
        file_path: publicUrl,
        file_size: file.size,
        mime_type: file.type,
        width,
        height,
        alt_text: metadata?.alt_text || '',
        caption: metadata?.caption || '',
        is_active: true,
        ...metadata
      };

      const { data: mediaRecord, error: dbError } = await supabase
        .from('blog_media')
        .insert([mediaData])
        .select()
        .single();

      if (dbError) throw dbError;
      return mediaRecord;
    } catch (error) {
      console.error('Error uploading media:', error);
      return null;
    }
  },

  async updateMedia(mediaId: string, updates: Partial<BlogMedia>): Promise<BlogMedia | null> {
    const { data, error } = await supabase
      .from('blog_media')
      .update(updates)
      .eq('id', mediaId)
      .select()
      .single();

    if (error) {
      console.error('Error updating media:', error);
      return null;
    }

    return data;
  },

  async deleteMedia(mediaId: string): Promise<boolean> {
    try {
      // Get media info first
      const media = await this.getMediaById(mediaId);
      if (!media) return false;

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('blog-media')
        .remove([`blog-media/${media.filename}`]);

      if (storageError) {
        console.error('Error deleting from storage:', storageError);
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('blog_media')
        .delete()
        .eq('id', mediaId);

      if (dbError) {
        console.error('Error deleting from database:', dbError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting media:', error);
      return false;
    }
  },

  // Team Management
  async getTeamMembers(activeOnly: boolean = true): Promise<TeamMember[]> {
    let query = supabase.from('team_members').select('*');

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query.order('display_order', { ascending: true });

    if (error) {
      console.error('Error fetching team members:', error);
      return [];
    }

    return data || [];
  },

  async getTeamMember(id: string): Promise<TeamMember | null> {
    const { data, error } = await supabase
      .from('team_members')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching team member:', error);
      return null;
    }

    return data;
  },

  async createTeamMember(memberData: Omit<TeamMember, 'id' | 'created_at' | 'updated_at'>): Promise<TeamMember | null> {
    const { data, error } = await supabase
      .from('team_members')
      .insert([memberData])
      .select()
      .single();

    if (error) {
      console.error('Error creating team member:', error);
      return null;
    }

    return data;
  },

  async updateTeamMember(id: string, memberData: Partial<TeamMember>): Promise<TeamMember | null> {
    const { data, error } = await supabase
      .from('team_members')
      .update(memberData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating team member:', error);
      return null;
    }

    return data;
  },

  async deleteTeamMember(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('team_members')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting team member:', error);
      return false;
    }

    return true;
  },

  async reorderTeamMembers(memberIds: string[]): Promise<boolean> {
    try {
      const updates = memberIds.map((id, index) =>
        supabase
          .from('team_members')
          .update({ display_order: index })
          .eq('id', id)
      );

      await Promise.all(updates);
      return true;
    } catch (error) {
      console.error('Error reordering team members:', error);
      return false;
    }
  },

  // Team Applications
  async getTeamApplications(status?: string): Promise<TeamApplication[]> {
    try {
      let query = supabase.from('team_applications').select('*');

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching team applications:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          status: status
        });
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching team applications:', error);
      return [];
    }
  },

  async getTeamApplication(id: string): Promise<TeamApplication | null> {
    const { data, error } = await supabase
      .from('team_applications')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching team application:', error);
      return null;
    }

    return data;
  },

  async createTeamApplication(applicationData: Omit<TeamApplication, 'id' | 'created_at' | 'updated_at' | 'status' | 'priority'>): Promise<TeamApplication | null> {
    const { data, error } = await supabase
      .from('team_applications')
      .insert([applicationData])
      .select()
      .single();

    if (error) {
      console.error('Error creating team application:', error);
      return null;
    }

    // Send account creation prompt email
    try {
      const { sendAccountCreationPrompt } = await import('./services/emailService');
      await sendAccountCreationPrompt(
        applicationData.name,
        applicationData.email,
        data.id
      );
    } catch (emailError) {
      console.error('Error sending account creation email:', emailError);
      // Don't fail the application creation if email fails
    }

    return data;
  },

  async updateTeamApplication(id: string, applicationData: Partial<TeamApplication>): Promise<TeamApplication | null> {
    const updateData = { ...applicationData };

    // Set reviewed_at timestamp if status is being changed from pending
    if (applicationData.status && applicationData.status !== 'pending' && !applicationData.reviewed_at) {
      updateData.reviewed_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('team_applications')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating team application:', error);
      return null;
    }

    return data;
  },

  async deleteTeamApplication(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('team_applications')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting team application:', error);
      return false;
    }

    return true;
  },

  // Chat Permissions
  async getChatPermissions(applicationId?: string): Promise<ChatPermission[]> {
    let query = supabase.from('chat_permissions').select('*');

    if (applicationId) {
      query = query.eq('application_id', applicationId);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching chat permissions:', error);
      return [];
    }

    return data || [];
  },

  async createChatPermission(permissionData: Omit<ChatPermission, 'id' | 'created_at' | 'updated_at'>): Promise<ChatPermission | null> {
    const { data, error } = await supabase
      .from('chat_permissions')
      .insert([permissionData])
      .select()
      .single();

    if (error) {
      console.error('Error creating chat permission:', error);
      return null;
    }

    return data;
  },

  async updateChatPermission(id: string, permissionData: Partial<ChatPermission>): Promise<ChatPermission | null> {
    const updateData = { ...permissionData, updated_at: new Date().toISOString() };

    const { data, error } = await supabase
      .from('chat_permissions')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating chat permission:', error);
      return null;
    }

    return data;
  },

  async toggleChatPermission(applicationId: string, userId: string, enabledBy: string, isEnabled: boolean): Promise<ChatPermission | null> {
    // First check if permission exists
    const { data: existing } = await supabase
      .from('chat_permissions')
      .select('*')
      .eq('application_id', applicationId)
      .eq('user_id', userId)
      .single();

    const permissionData = {
      is_enabled: isEnabled,
      enabled_by: enabledBy,
      enabled_at: isEnabled ? new Date().toISOString() : undefined,
      disabled_at: !isEnabled ? new Date().toISOString() : undefined,
      updated_at: new Date().toISOString()
    };

    let result: ChatPermission | null = null;

    if (existing) {
      // Update existing permission
      result = await this.updateChatPermission(existing.id, permissionData);
    } else {
      // Create new permission
      result = await this.createChatPermission({
        user_id: userId,
        application_id: applicationId,
        ...permissionData
      });
    }

    // Send email notification if chat was enabled
    if (result && isEnabled) {
      try {
        // Get application and user details
        const application = await this.getTeamApplication(applicationId);
        const userProfile = await this.getProfile(userId);

        if (application && userProfile) {
          const { sendChatEnabledNotification } = await import('./services/emailService');
          await sendChatEnabledNotification(
            application.name,
            application.email,
            application.desired_role
          );
        }
      } catch (emailError) {
        console.error('Error sending chat enabled email:', emailError);
        // Don't fail the permission toggle if email fails
      }
    }

    return result;
  },

  // Chat Rooms
  async getChatRooms(userId: string, type?: 'applicant_admin' | 'admin_only'): Promise<ChatRoom[]> {
    try {
      // Validate input
      if (!userId || typeof userId !== 'string') {
        console.error('Invalid userId provided to getChatRooms:', userId);
        return [];
      }

      console.log(`Fetching chat rooms for user: ${userId}, type: ${type || 'all'}`);

      // First get room IDs where user is a participant
      const { data: participantRooms, error: participantError } = await supabase
        .from('chat_room_participants')
        .select('room_id')
        .eq('user_id', userId);

      if (participantError) {
        console.error('Error fetching participant rooms:', {
          error: participantError,
          message: participantError.message,
          details: participantError.details,
          hint: participantError.hint,
          code: participantError.code,
          userId: userId
        });

        // If it's a permission error, provide more context
        if (participantError.code === 'PGRST301' || participantError.message?.includes('permission')) {
          console.error('Permission denied accessing chat room participants. Check RLS policies.');
        }

        return [];
      }

      if (!participantRooms || participantRooms.length === 0) {
        console.log('No chat room participations found for user:', userId);
        return [];
      }

      const roomIds = participantRooms.map(p => p.room_id).filter(Boolean);
      console.log(`Found ${roomIds.length} room IDs for user:`, roomIds);

      if (roomIds.length === 0) {
        console.log('No valid room IDs found for user:', userId);
        return [];
      }

      // Build the query step by step for better debugging
      let query = supabase
        .from('chat_rooms')
        .select(`
          *,
          participants:chat_room_participants(
            *,
            user:profiles(*)
          )
        `)
        .eq('is_active', true)
        .in('id', roomIds);

      if (type) {
        console.log(`Filtering by type: ${type}`);
        query = query.eq('type', type);
      }

      console.log('Executing chat rooms query...');
      const { data, error } = await query
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching chat rooms:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          roomIds: roomIds,
          userId: userId,
          type: type
        });

        // Try a fallback approach with simpler query
        console.log('Attempting fallback query without participants join...');
        try {
          let fallbackQuery = supabase
            .from('chat_rooms')
            .select('*')
            .eq('is_active', true)
            .in('id', roomIds);

          if (type) {
            fallbackQuery = fallbackQuery.eq('type', type);
          }

          const { data: fallbackData, error: fallbackError } = await fallbackQuery
            .order('updated_at', { ascending: false });

          if (fallbackError) {
            console.error('Fallback query also failed:', fallbackError);
            return [];
          }

          console.log('Fallback query succeeded, fetching participants separately...');
          const roomsWithParticipants = await Promise.all(
            (fallbackData || []).map(async (room) => {
              try {
                const { data: participants } = await supabase
                  .from('chat_room_participants')
                  .select(`
                    *,
                    user:profiles(*)
                  `)
                  .eq('room_id', room.id);

                return { ...room, participants: participants || [] };
              } catch (error) {
                console.error(`Error fetching participants for room ${room.id}:`, error);
                return { ...room, participants: [] };
              }
            })
          );

          console.log(`Fallback approach succeeded with ${roomsWithParticipants.length} rooms`);
          return roomsWithParticipants;
        } catch (fallbackError) {
          console.error('Fallback approach failed:', fallbackError);
        }

        // Provide more specific error context
        if (error.code === 'PGRST301' || error.message?.includes('permission')) {
          console.error('Permission denied accessing chat rooms. Check RLS policies.');
        } else if (error.code === 'PGRST116') {
          console.log('No chat rooms found matching criteria');
          return [];
        } else if (error.code === '42601' || error.message?.includes('syntax')) {
          console.error('SQL syntax error in chat rooms query');
        }

        return [];
      }

      // Filter out any null/undefined results and ensure proper structure
      const validRooms = (data || []).filter(room => room && room.id);

      console.log(`Successfully fetched ${validRooms.length} chat rooms for user ${userId}:`,
        validRooms.map(r => ({ id: r.id, type: r.type, name: r.name })));

      return validRooms;

    } catch (error) {
      console.error('Unexpected error fetching chat rooms:', {
        error,
        userId,
        type,
        stack: error instanceof Error ? error.stack : undefined
      });
      return [];
    }
  },

  async createChatRoom(roomData: Omit<ChatRoom, 'id' | 'created_at' | 'updated_at' | 'participants' | 'last_message' | 'unread_count'>): Promise<ChatRoom | null> {
    const { data, error } = await supabase
      .from('chat_rooms')
      .insert([roomData])
      .select()
      .single();

    if (error) {
      console.error('Error creating chat room:', error);
      return null;
    }

    return data;
  },

  async addChatRoomParticipant(roomId: string, userId: string): Promise<ChatRoomParticipant | null> {
    const { data, error } = await supabase
      .from('chat_room_participants')
      .insert([{ room_id: roomId, user_id: userId }])
      .select()
      .single();

    if (error) {
      console.error('Error adding chat room participant:', error);
      return null;
    }

    return data;
  },

  async createApplicantChatRoom(applicationId: string, applicantUserId: string, adminUserId: string): Promise<ChatRoom | null> {
    // Create the chat room
    const room = await this.createChatRoom({
      type: 'applicant_admin',
      application_id: applicationId,
      created_by: adminUserId,
      is_active: true
    });

    if (!room) return null;

    // Add participants
    await this.addChatRoomParticipant(room.id, applicantUserId);
    await this.addChatRoomParticipant(room.id, adminUserId);

    return room;
  },

  // Chat Messages
  async getChatMessages(roomId: string, limit: number = 50, offset: number = 0): Promise<ChatMessage[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        sender:profiles(*)
      `)
      .eq('room_id', roomId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching chat messages:', error);
      return [];
    }

    return (data || []).reverse(); // Reverse to show oldest first
  },

  async sendChatMessage(messageData: Omit<ChatMessage, 'id' | 'created_at' | 'sender' | 'is_edited' | 'edited_at'>): Promise<ChatMessage | null> {
    // Import security functions dynamically to avoid circular imports
    const {
      checkMessageRateLimit,
      validateChatPermission,
      moderateContent,
      logChatAction
    } = await import('./security/chatSecurity');

    try {
      // Validate permissions
      const permissionCheck = await validateChatPermission(messageData.sender_id, messageData.room_id);
      if (!permissionCheck.allowed) {
        console.error('Permission denied:', permissionCheck.reason);
        throw new Error(permissionCheck.reason || 'Permission denied');
      }

      // Check rate limits
      const rateLimitOk = await checkMessageRateLimit(messageData.sender_id);
      if (!rateLimitOk) {
        throw new Error('Rate limit exceeded. Please wait before sending another message.');
      }

      // Moderate and sanitize content
      const moderation = moderateContent(messageData.message);
      if (!moderation.approved) {
        throw new Error(moderation.reason || 'Message content not approved');
      }

      // Prepare sanitized message data
      const sanitizedMessageData = {
        ...messageData,
        message: moderation.sanitized
      };

      const { data, error } = await supabase
        .from('chat_messages')
        .insert([sanitizedMessageData])
        .select(`
          *,
          sender:profiles(*)
        `)
        .single();

      if (error) {
        console.error('Error sending chat message:', error);
        return null;
      }

      // Update room's updated_at timestamp
      await supabase
        .from('chat_rooms')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', messageData.room_id);

      // Log the action
      await logChatAction('message_sent', {
        messageId: data.id,
        roomId: messageData.room_id,
        messageType: messageData.message_type
      }, messageData.sender_id);

      // Send email notifications to other participants (async, don't wait)
      this.sendMessageNotifications(data).catch((error: unknown) => {
        console.error('Error sending message notifications:', error);
      });

      return data;
    } catch (error) {
      console.error('Error in sendChatMessage:', error);
      throw error;
    }
  },

  async updateLastReadAt(roomId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_room_participants')
      .update({ last_read_at: new Date().toISOString() })
      .eq('room_id', roomId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating last read timestamp:', error);
    }
  },

  // Account Creation Tracking
  async markAccountCreated(applicationId: string, userId: string): Promise<TeamApplication | null> {
    const { data, error } = await supabase
      .from('team_applications')
      .update({
        account_created: true,
        account_created_at: new Date().toISOString(),
        user_id: userId,
        updated_at: new Date().toISOString()
      })
      .eq('id', applicationId)
      .select()
      .single();

    if (error) {
      console.error('Error marking account as created:', error);
      return null;
    }

    return data;
  },

  async getApplicationByEmail(email: string): Promise<TeamApplication | null> {
    const { data, error } = await supabase
      .from('team_applications')
      .select('*')
      .eq('email', email)
      .eq('account_created', false)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.error('Error fetching application by email:', error);
      return null;
    }

    return data;
  },

  // Admin Users
  async getAdminUsers(): Promise<Profile[]> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .or('role.eq.admin,<EMAIL>')
      .order('display_name', { ascending: true });

    if (error) {
      console.error('Error fetching admin users:', error);
      return [];
    }

    return data || [];
  },

  // Email Notifications
  async sendMessageNotifications(message: ChatMessage): Promise<void> {
    try {
      // Get room participants (excluding the sender)
      const { data: participants, error: participantsError } = await supabase
        .from('chat_room_participants')
        .select(`
          user_id,
          user:profiles(*)
        `)
        .eq('room_id', message.room_id)
        .neq('user_id', message.sender_id);

      if (participantsError) {
        console.error('Error fetching participants for notifications:', participantsError);
        return;
      }

      if (!participants || participants.length === 0) {
        return;
      }

      // Get room and application details
      const { data: room, error: roomError } = await supabase
        .from('chat_rooms')
        .select('*, application_id')
        .eq('id', message.room_id)
        .single();

      if (roomError || !room) {
        console.error('Error fetching room for notifications:', roomError);
        return;
      }

      let applicationRole = 'Unknown Role';
      if (room.application_id) {
        const application = await this.getTeamApplication(room.application_id);
        if (application) {
          applicationRole = application.desired_role;
        }
      }

      // Get sender details
      const senderProfile = await this.getProfile(message.sender_id);
      const senderName = senderProfile?.display_name || senderProfile?.email || 'Team Member';

      // Send notifications to all participants
      const { sendNewMessageNotification } = await import('./services/emailService');

      for (const participant of participants) {
        const user = participant.user as unknown as Profile;
        if (user && user.email) {
          await sendNewMessageNotification(
            user.display_name || user.email || 'User',
            user.email,
            senderName,
            message.message,
            applicationRole
          );
        }
      }
    } catch (error) {
      console.error('Error in sendMessageNotifications:', error);
    }
  }
};

export default supabase;
