'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { db, HielProfile } from '@/lib/supabase';
import { motion } from 'framer-motion';
import HielPublicProfile from '@/components/hiellinks/HielPublicProfile';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { username: string } }): Promise<Metadata> {
  try {
    const profile = await db.getHielProfile(params.username);

    if (!profile) {
      return {
        title: 'Profile Not Found - HielLinks',
        description: 'The requested profile could not be found.',
      };
    }

    return {
      title: `${profile.business_name} (@${profile.username}) - HielLinks`,
      description: profile.description || `Check out ${profile.business_name}'s links and contact information.`,
      openGraph: {
        title: profile.business_name,
        description: profile.description || `Check out ${profile.business_name}'s links and contact information.`,
        images: profile.logo_url ? [profile.logo_url] : undefined,
        type: 'profile',
      },
      twitter: {
        card: 'summary',
        title: profile.business_name,
        description: profile.description || `Check out ${profile.business_name}'s links and contact information.`,
        images: profile.logo_url ? [profile.logo_url] : undefined,
      },
    };
  } catch (error) {
    return {
      title: 'HielLinks Profile',
      description: 'Business profile and links',
    };
  }
}

export default function HielLinksProfilePage() {
  const params = useParams();
  const username = params.username as string;
  const [profile, setProfile] = useState<HielProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    if (username) {
      loadProfile();
    }
  }, [username]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setNotFound(false);
      
      const profileData = await db.getHielProfile(username);
      
      if (profileData) {
        setProfile(profileData);
        
        // Track profile view
        await db.trackHielAnalytics({
          profile_id: profileData.id,
          event_type: 'profile_view',
          visitor_ip: undefined, // Will be handled by server if needed
          user_agent: navigator.userAgent,
          referrer: document.referrer || undefined,
        });

        // Increment profile view count
        await db.incrementProfileView(profileData.id);
      } else {
        setNotFound(true);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      setNotFound(true);
    } finally {
      setLoading(false);
    }
  };

  const handleLinkClick = async (linkId: string, url: string) => {
    if (profile) {
      // Track link click
      await db.trackHielAnalytics({
        profile_id: profile.id,
        link_id: linkId,
        event_type: 'link_click',
        visitor_ip: undefined,
        user_agent: navigator.userAgent,
        referrer: document.referrer || undefined,
      });

      // Increment link click count
      await db.incrementLinkClick(linkId);
    }

    // Open link
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner />
      </div>
    );
  }

  if (notFound || !profile) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="text-6xl mb-4">🔍</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Profile Not Found
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The profile "@{username}" doesn't exist or is not published.
          </p>
          <a
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Go to Homepage
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: profile.theme_color + '10' }}>
      <HielPublicProfile 
        profile={profile} 
        onLinkClick={handleLinkClick}
      />
    </div>
  );
}
